#!/system/bin/sh
# TNut_Android_simple.sh - 简化版本
# By.举个🌰
# Copyright (c) 2025

# 等待系统完全启动
sleep 10

# 设置ADB TCP端口为5555
setprop service.adb.tcp.port 5555
stop adbd
sleep 1
start adbd
sleep 2

# 设置所有相关属性
setprop persist.sys.epg.launcher.packagename com.dangbei.tvlauncher
setprop persist.sys.epg.launcher.classname com.dangbei.launcher.ui.main.MainActivity
setprop epg.launcher.packagename com.dangbei.tvlauncher
setprop epg.launcher.classname com.dangbei.launcher.ui.main.MainActivity
setprop sys.install.mode normal
setprop ro.config.default_launcher com.dangbei.tvlauncher
setprop persist.vendor.launcher.packagename com.dangbei.tvlauncher
setprop persist.vendor.launcher.classname com.dangbei.launcher.ui.main.MainActivity
setprop persist.sys.home.packagename com.dangbei.tvlauncher
setprop persist.sys.home.classname com.dangbei.launcher.ui.main.MainActivity

# 强制设置默认启动器
pm set-home-activity com.dangbei.tvlauncher/com.dangbei.launcher.ui.main.MainActivity

# 启动当贝桌面
am start -n com.dangbei.tvlauncher/com.dangbei.launcher.ui.main.MainActivity

# 简单的监控循环 - 每5秒检查一次
while true; do
    sleep 5
    # 如果当前不是当贝桌面在前台，且没有其他应用在运行，则启动当贝桌面
    current_activity=$(dumpsys activity activities | grep "mResumedActivity" | head -1)
    if ! echo "$current_activity" | grep -q "com.dangbei.tvlauncher"; then
        # 检查是否在桌面状态（没有其他应用运行）
        if echo "$current_activity" | grep -q "Launcher\|Home" || [ -z "$current_activity" ]; then
            am start -n com.dangbei.tvlauncher/com.dangbei.launcher.ui.main.MainActivity
        fi
    fi
done
