#!/system/bin/sh
# TNut_Android.sh - 晶晨机顶盒Android 9启动脚本
# By.举个🌰
# Copyright (c) 2025

# 等待系统完全启动
sleep 10

# 设置ADB TCP端口为5555
setprop service.adb.tcp.port 5555

# 停止ADB守护进程
stop adbd

# 等待一秒确保服务完全停止
sleep 1

# 启动ADB守护进程
start adbd

# 等待ADB服务启动
sleep 2

# 设置EPG启动器包名和类名（持久化属性）
setprop persist.sys.epg.launcher.packagename com.dangbei.tvlauncher
setprop persist.sys.epg.launcher.classname com.dangbei.launcher.ui.main.MainActivity

# 设置EPG启动器包名和类名（运行时属性）
setprop epg.launcher.packagename com.dangbei.tvlauncher
setprop epg.launcher.classname com.dangbei.launcher.ui.main.MainActivity

# 设置安装模式为normal
setprop sys.install.mode normal

# 设置默认启动器相关属性（晶晨机顶盒特有）
setprop ro.config.default_launcher com.dangbei.tvlauncher
setprop persist.vendor.launcher.packagename com.dangbei.tvlauncher
setprop persist.vendor.launcher.classname com.dangbei.launcher.ui.main.MainActivity

# 强制设置首页键行为
setprop persist.sys.home.packagename com.dangbei.tvlauncher
setprop persist.sys.home.classname com.dangbei.launcher.ui.main.MainActivity

# 重启launcher服务以应用更改
pkill -f com.android.launcher
pkill -f launcher

# 启动当贝桌面
am start -n com.dangbei.tvlauncher/com.dangbei.launcher.ui.main.MainActivity

exit 0
