#!/system/bin/sh
# TNut_Android.sh - 晶晨机顶盒Android 9启动脚本
# By.举个🌰
# Copyright (c) 2025

# 日志文件路径
LOG_FILE="/data/local/tmp/tnut_android.log"

# 记录日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# 开始执行
log_message "TNut_Android.sh 脚本开始执行"

# 等待系统完全启动
sleep 10

# 设置ADB TCP端口为5555
log_message "设置ADB TCP端口为5555"
setprop service.adb.tcp.port 5555

# 停止ADB守护进程
log_message "停止ADB守护进程"
stop adbd

# 等待一秒确保服务完全停止
sleep 1

# 启动ADB守护进程
log_message "启动ADB守护进程"
start adbd

# 等待ADB服务启动
sleep 2

# 设置EPG启动器包名和类名（持久化属性）
log_message "设置EPG启动器持久化属性"
setprop persist.sys.epg.launcher.packagename com.dangbei.tvlauncher
setprop persist.sys.epg.launcher.classname com.dangbei.launcher.ui.main.MainActivity

# 设置EPG启动器包名和类名（运行时属性）
log_message "设置EPG启动器运行时属性"
setprop epg.launcher.packagename com.dangbei.tvlauncher
setprop epg.launcher.classname com.dangbei.launcher.ui.main.MainActivity

# 设置安装模式为normal
log_message "设置安装模式为normal"
setprop sys.install.mode normal

# 记录完成状态
log_message "TNut_Android.sh 脚本执行完成"

# 验证设置是否生效
log_message "验证ADB TCP端口设置: $(getprop service.adb.tcp.port)"
log_message "验证EPG启动器包名: $(getprop persist.sys.epg.launcher.packagename)"
log_message "验证安装模式: $(getprop sys.install.mode)"

exit 0
