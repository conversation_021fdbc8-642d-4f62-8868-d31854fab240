#!/system/bin/sh
# start_monitor.sh - 启动监控脚本
# By.举个🌰
# Copyright (c) 2025

# 等待系统启动
sleep 20

# 初始设置
setprop service.adb.tcp.port 5555
stop adbd
sleep 1
start adbd

# 设置所有属性
setprop persist.sys.epg.launcher.packagename com.dangbei.tvlauncher
setprop persist.sys.epg.launcher.classname com.dangbei.launcher.ui.main.MainActivity
setprop epg.launcher.packagename com.dangbei.tvlauncher
setprop epg.launcher.classname com.dangbei.launcher.ui.main.MainActivity
setprop sys.install.mode normal

# 启动当贝桌面
am start -n com.dangbei.tvlauncher/com.dangbei.launcher.ui.main.MainActivity

# 每5秒运行一次监控脚本
while true; do
    sleep 5
    /system/bin/home_key_monitor.sh
done
