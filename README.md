# Android TV 首页键延迟修复项目 - “幽灵”重定向应用开发文档

**By. 举个🌰**

**Copyright 2025**

---

## 1. 问题背景

- **设备**: CM311-1e 型号电视盒子 (AmlogicS905L3S 主板)
- **现象**: 在刷入当贝桌面 (`com.dangbei.tvlauncher`) 后，每次按下遥控器的“首页”键，系统会延迟 5-10 秒才返回桌面。
- **根本原因**: 经过深入的静态与动态分析，最终定位到问题根源在于系统固件的核心文件 `/system/framework/oat/arm/services.vdex` 中，硬编码（Hardcoded）了一个系统属性 `epg.launcher.packagename`，其值被强制设定为 `com.aspirecn.activate`。

因此，当用户按下首页键时，系统会顽固地尝试启动这个不存在的 `com.aspirecn.activate` 应用。在经历漫长的等待超时后，系统才会放弃并回退（Fallback）到当前实际的默认桌面（当贝桌面），这个等待过程就是延迟的直接原因。

---

## 2. 解决方案

直接修改 `services.vdex` 文件是绝对禁止的，这会导致系统完整性校验失败，造成设备“变砖”。

因此，我们采用了一种安全、无害且极为巧妙的“移花接木”方案：**创建一个“幽灵”重定向应用（Shim App）**。

- **伪装**: 这个应用的包名 (`applicationId`) 被精确地设置为 `com.aspirecn.activate`，以此来“欺骗”系统。
- **响应**: 应用内包含一个 Activity，并注册为系统默认的 `HOME` 应用，使其能够响应首页键的启动请求。
- **重定向**: 当系统启动这个“幽灵”应用时，它在 `onCreate` 生命周期中的唯一任务，就是立刻发出一个启动真正当贝桌面的 `Intent` 请求。
- **消失**: 在发出请求后，它会立刻调用 `finish()` 方法自我销毁。同时，它的界面主题被设置为 `@android:style/Theme.NoDisplay`，确保整个过程用户毫无察觉，没有任何界面闪烁。

通过这个流程，我们将系统一次错误的启动请求，无缝地重定向到正确的应用上，从而彻底消除了延迟。

---

## 3. 项目实现细节

### 3.1 `app/build.gradle.kts` (编译配置文件)

这是项目的编译说明书，最核心的修改是：

```kotlin
// ...
android {
    // ...
    defaultConfig {
        // 必须设置为这个值，以匹配固件中的硬编码
        applicationId = "com.aspirecn.activate"
        // ...
    }
}
// ...
```

### 3.2 `app/src/main/AndroidManifest.xml` (应用清单文件)

这是应用的“户口本”，核心配置如下：

```xml
<application
    android:label="Home Redirect" // 应用名，可自定义
    android:theme="@android:style/Theme.NoDisplay"> // 核心：设置为无界面主题

    <activity
        android:name=".MainActivity" 
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            // 核心：将自己注册为 HOME 应用
            <category android:name="android.intent.category.HOME" />
            <category android:name="android.intent.category.DEFAULT" />
        </intent-filter>
    </activity>

</application>
```

### 3.3 `app/src/main/java/com/aspirecn/activate/MainActivity.java` (核心逻辑)

这是“幽灵”应用的心脏，代码逻辑清晰明了：

```java
package com.aspirecn.activate;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

public class MainActivity extends Activity {

    // 目标桌面的包名
    private static final String TARGET_LAUNCHER_PACKAGE = "com.dangbei.tvlauncher";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 1. 获取目标桌面的启动意图
        Intent intent = getPackageManager().getLaunchIntentForPackage(TARGET_LAUNCHER_PACKAGE);

        if (intent != null) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            try {
                // 2. 启动目标桌面
                startActivity(intent);
            } catch (Exception e) {
                // 异常处理
            }
        }

        // 3. 立刻自我销毁
        finish();
    }
}
```

---

## 4. 如何编译与安装

1.  **打开项目**: 使用 Android Studio 打开本项目 (`nut` 文件夹)。
2.  **编译APK**: 在菜单栏选择 `Build` -> `Build Bundle(s) / APK(s)` -> `Build APK(s)`。
3.  **找到APK**: 编译成功后，在 `nut/app/build/outputs/apk/debug/` 目录下会生成 `app-debug.apk` 文件。
4.  **安装APK**: 使用 adb 命令进行安装：
    ```bash
    adb install /path/to/your/app-debug.apk
    ```

安装成功后，问题即可解决。如果想恢复原状，只需卸载这个“Home Redirect”应用即可，对系统无任何风险。
