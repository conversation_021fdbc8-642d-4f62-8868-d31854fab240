#!/system/bin/sh
# TNut_Android_v2.sh - 更稳定的版本
# By.举个🌰
# Copyright (c) 2025

# 等待系统完全启动
sleep 15

# 设置ADB TCP端口为5555
setprop service.adb.tcp.port 5555
stop adbd
sleep 1
start adbd
sleep 2

# 设置所有相关属性
setprop persist.sys.epg.launcher.packagename com.dangbei.tvlauncher
setprop persist.sys.epg.launcher.classname com.dangbei.launcher.ui.main.MainActivity
setprop epg.launcher.packagename com.dangbei.tvlauncher
setprop epg.launcher.classname com.dangbei.launcher.ui.main.MainActivity
setprop sys.install.mode normal
setprop ro.config.default_launcher com.dangbei.tvlauncher
setprop persist.vendor.launcher.packagename com.dangbei.tvlauncher
setprop persist.vendor.launcher.classname com.dangbei.launcher.ui.main.MainActivity
setprop persist.sys.home.packagename com.dangbei.tvlauncher
setprop persist.sys.home.classname com.dangbei.launcher.ui.main.MainActivity

# 强制设置默认启动器
pm set-home-activity com.dangbei.tvlauncher/com.dangbei.launcher.ui.main.MainActivity

# 启动当贝桌面
am start -n com.dangbei.tvlauncher/com.dangbei.launcher.ui.main.MainActivity

# 创建一个简单的监控脚本，每30秒运行一次
while true; do
    sleep 30
    
    # 简单检查：如果当贝桌面不在运行，就启动它
    if ! pgrep -f "com.dangbei.tvlauncher" > /dev/null 2>&1; then
        am start -n com.dangbei.tvlauncher/com.dangbei.launcher.ui.main.MainActivity
    fi
    
    # 每5分钟重新设置一次默认启动器
    if [ $(($(date +%s) % 300)) -lt 30 ]; then
        pm set-home-activity com.dangbei.tvlauncher/com.dangbei.launcher.ui.main.MainActivity
    fi
done &

# 主进程保持运行
while true; do
    sleep 3600  # 每小时检查一次
    
    # 检查后台监控进程是否还在运行
    if ! pgrep -f "sleep 30" > /dev/null 2>&1; then
        # 如果监控进程死了，重新启动
        exec $0
    fi
done
